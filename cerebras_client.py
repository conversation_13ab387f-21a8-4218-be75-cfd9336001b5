"""
Модуль для работы с Cerebras API (Llama 4 Maverick).
Используется для определения сложности запросов пользователей.
"""

import requests
import json
import time
import random
from typing import Optional, Dict, Any
from bot_globals import log_admin

# API ключ и настройки для Cerebras
CEREBRAS_API_KEY = "csk-2rt6htct4nvwmpd6234kyym23d6pc6h5jfh533ykc38n6jed"
CEREBRAS_API_URL = "https://api.cerebras.ai/v1/chat/completions"
CEREBRAS_MODEL = "llama-3.3-70b"

# Системный промпт для определения сложности запроса
COMPLEXITY_SYSTEM_PROMPT = """Ты эксперт по анализу сложности текстовых запросов. Твоя задача - определить сложность запроса пользователя и ответить СТРОГО только одним тегом.

ПРАВИЛА КЛАССИФИКАЦИИ:
- [LITE] - только для самых простых вопросов: приветствия, базовые факты, простая арифметика, переводы отдельных слов
- [HARD] - для обычных сложных задач: анализ, программирование, объяснения, творческие задачи, специализированные знания
- [ULTRAPRO] - для задач требующих ГЛУБОКОГО размышления: философия, этика, сложные научные концепции, многофакторный анализ, теоретические модели, парадоксы, междисциплинарные вопросы

КРИТЕРИИ ДЛЯ [ULTRAPRO] (выбирай чаще):
- Философские вопросы любой сложности
- Этические дилеммы и моральные вопросы
- Научные теории и концепции (квантовая физика, космология, нейронауки)
- Анализ сложных социальных/политических/экономических явлений
- Вопросы о сознании, разуме, искусственном интеллекте
- Парадоксы и противоречия
- Междисциплинарные задачи
- Глобальные проблемы человечества
- Футурология и прогнозирование
- Сложные творческие задачи с множеством ограничений

ПРИМЕРЫ [LITE]:
- "Привет, как дела?"
- "Сколько будет 2+2?"
- "Переведи слово hello"

ПРИМЕРЫ [HARD]:
- "Напиши код на Python"
- "Объясни как работает интернет"
- "Создай план тренировок"

ПРИМЕРЫ [ULTRAPRO]:
- "Что такое сознание?"
- "Как решить проблему изменения климата?"
- "Объясни квантовую запутанность"
- "Этично ли создавать ИИ?"
- "Как будет выглядеть будущее человечества?"
- "В чем смысл жизни?"
- "Как работает время?"
- "Что такое справедливость?"
- "Возможно ли бессмертие?"
- "Как возникла вселенная?"

ВАЖНО: При сомнении между HARD и ULTRAPRO - выбирай ULTRAPRO! Отвечай ТОЛЬКО тегом [LITE], [HARD] или [ULTRAPRO]. Никакого другого текста!"""


class CerebrasClient:
    """Клиент для работы с Cerebras API."""
    
    def __init__(self):
        self.api_key = CEREBRAS_API_KEY
        self.api_url = CEREBRAS_API_URL
        self.model = CEREBRAS_MODEL
        self.timeout = 10  # Быстрый таймаут для определения сложности
        
    def classify_complexity(self, user_query: str) -> str:
        """
        Определяет сложность запроса пользователя.
        
        Args:
            user_query: Запрос пользователя
            
        Returns:
            "LITE", "HARD" или "ULTRAPRO" в зависимости от сложности
        """
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            payload = {
                "model": self.model,
                "messages": [
                    {
                        "role": "system",
                        "content": COMPLEXITY_SYSTEM_PROMPT
                    },
                    {
                        "role": "user", 
                        "content": user_query
                    }
                ],
                "max_tokens": 10,  # Очень мало токенов, нужен только тег
                "temperature": 0.1,  # Низкая температура для стабильности
                "stream": False
            }
            
            start_time = time.time()
            response = requests.post(
                self.api_url,
                headers=headers,
                json=payload,
                timeout=self.timeout
            )
            duration = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                content = result.get("choices", [{}])[0].get("message", {}).get("content", "").strip()
                
                # Парсим ответ
                if "[ULTRAPRO]" in content.upper():
                    complexity = "ULTRAPRO"
                elif "[HARD]" in content.upper():
                    complexity = "HARD"
                elif "[LITE]" in content.upper():
                    complexity = "LITE"
                else:
                    # Fallback - если не удалось распознать, считаем сложным
                    complexity = "HARD"
                    log_admin(f"[Cerebras] Unexpected response format: '{content}', defaulting to HARD", level="warning")
                
                log_admin(f"[Cerebras] Query classified as {complexity} in {duration:.2f}s", level="debug")
                return complexity
                
            else:
                log_admin(f"[Cerebras] API error {response.status_code}: {response.text}", level="error")
                return "HARD"  # Fallback к сложному при ошибке
                
        except requests.exceptions.Timeout:
            log_admin("[Cerebras] Request timeout, defaulting to HARD", level="warning")
            return "HARD"
        except Exception as e:
            log_admin(f"[Cerebras] Error classifying complexity: {e}", level="error")
            return "HARD"  # Fallback к сложному при ошибке


# Глобальный экземпляр клиента
cerebras_client = CerebrasClient()


def classify_query_complexity(user_query: str) -> str:
    """
    Определяет сложность запроса пользователя через Llama 4 Maverick.
    
    Args:
        user_query: Запрос пользователя
        
    Returns:
        "LITE", "HARD" или "ULTRAPRO"
    """
    return cerebras_client.classify_complexity(user_query)