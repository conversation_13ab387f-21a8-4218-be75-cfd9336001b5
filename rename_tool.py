# Модуль для обработки скрытого инструмента [RENAME:префикс]
# Позволяет ИИ автоматически переименовывать администраторов в групповых чатах

import re
import telebot
from bot_globals import bot, log_admin

def process_rename_tool(response_text, message):
    """
    Обрабатывает скрытый инструмент [RENAME:префикс] в ответах ИИ
    
    Args:
        response_text (str): Текст ответа от ИИ
        message: Объект сообщения Telegram
        
    Returns:
        str: Очищенный от [RENAME:] тегов текст ответа
    """
    # Искать паттерн [RENAME:префикс]
    rename_pattern = r'\[RENAME:([^\]]+)\]'
    
    matches = re.findall(rename_pattern, response_text)
    
    if matches and message.chat.type in ['group', 'supergroup']:
        new_title = matches[0]
        
        try:
            # Проверить права бота
            bot_member = bot.get_chat_member(message.chat.id, bot.get_me().id)
            if bot_member.status != 'administrator' or not bot_member.can_promote_members:
                log_admin(f"Bot doesn't have admin rights in chat {message.chat.id}", level="debug")
                # Удаляем [RENAME:] из ответа и возвращаем
                clean_response = re.sub(rename_pattern, '', response_text)
                return clean_response
            
            # Проверить статус пользователя
            user_member = bot.get_chat_member(message.chat.id, message.from_user.id)
            if user_member.status not in ['administrator', 'creator']:
                log_admin(f"User {message.from_user.id} is not admin in chat {message.chat.id}", level="debug")
                # Удаляем [RENAME:] из ответа и возвращаем
                clean_response = re.sub(rename_pattern, '', response_text)
                return clean_response
            
            # Попытаться установить новый префикс
            bot.set_chat_administrator_custom_title(
                chat_id=message.chat.id,
                user_id=message.from_user.id,
                custom_title=new_title
            )
            log_admin(f"Successfully renamed admin {message.from_user.id} to '{new_title}' in chat {message.chat.id}", level="info")
            
        except Exception as e:
            # Игнорировать ошибки молча
            log_admin(f"Error in rename tool: {e}", level="debug")
    
    # ОБЯЗАТЕЛЬНО удалить все упоминания [RENAME:] из ответа
    clean_response = re.sub(rename_pattern, '', response_text)
    return clean_response


def check_rename_permissions(message):
    """
    Проверяет, может ли бот использовать функцию переименования в данном чате
    
    Args:
        message: Объект сообщения Telegram
        
    Returns:
        dict: Словарь с информацией о правах
    """
    result = {
        'can_rename': False,
        'is_group': False,
        'bot_is_admin': False,
        'bot_can_promote': False,
        'user_is_admin': False
    }
    
    try:
        # Проверяем тип чата
        if message.chat.type in ['group', 'supergroup']:
            result['is_group'] = True
            
            # Проверяем права бота
            bot_member = bot.get_chat_member(message.chat.id, bot.get_me().id)
            if bot_member.status == 'administrator':
                result['bot_is_admin'] = True
                if bot_member.can_promote_members:
                    result['bot_can_promote'] = True
                    
                    # Проверяем статус пользователя
                    user_member = bot.get_chat_member(message.chat.id, message.from_user.id)
                    if user_member.status in ['administrator', 'creator']:
                        result['user_is_admin'] = True
                        result['can_rename'] = True
                        
    except Exception as e:
        log_admin(f"Error checking rename permissions: {e}", level="debug")
    
    return result
